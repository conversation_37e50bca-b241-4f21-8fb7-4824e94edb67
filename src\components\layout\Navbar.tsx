import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "./AuthProvider";
import { Menu, X, MessageCircle, ChevronDown } from "lucide-react";
import NavbarUserMenu from "./NavbarUserMenu";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [visibleItems, setVisibleItems] = useState<number>(3); // Default to show 3 items
  const { user } = useAuth();
  const navRef = useRef<HTMLElement>(null);

  // Navigation items that can be collapsed on tablet
  const primaryNavItems = [
    { to: "/listings", label: "Properties" },
    { to: "/hotels", label: "Hotels" },
    { to: "/cars", label: "Car Rentals" },
  ];

  const secondaryNavItems = [
    {
      href: "https://gescogm.com/#/produits/categorie/2840",
      label: "Restaurants",
      external: true,
    },
    { href: "http://e-tickets.online", label: "Night Life", external: true },
    { href: "/#about", label: "About" },
  ];

  const userNavItems = user
    ? [
        { to: "/listings/create", label: "List Property" },
        { to: "/hotels/create", label: "List Hotel" },
        { to: "/cars/create", label: "List Car" },
      ]
    : [];

  // Combine all navigation items for responsive handling
  const allNavItems = [
    ...primaryNavItems.map((item) => ({ ...item, external: false })),
    ...secondaryNavItems.map((item) => ({
      to: item.href,
      label: item.label,
      external: item.external || false,
    })),
    ...userNavItems.map((item) => ({ ...item, external: false })),
  ];

  // Effect to handle responsive navigation
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      // Adjust visible items based on screen width - use compact style for all sizes
      if (width >= 768) {
        // md and above - show items based on available space with compact style
        if (width >= 1200) {
          setVisibleItems(Math.min(6, allNavItems.length)); // Show up to 6 items
        } else if (width >= 1024) {
          setVisibleItems(Math.min(5, allNavItems.length)); // Show up to 5 items
        } else if (width >= 950) {
          setVisibleItems(4); // Show 4 items + More
        } else if (width >= 850) {
          setVisibleItems(3); // Show 3 items + More
        } else if (width >= 780) {
          setVisibleItems(2); // Show 2 items + More
        } else {
          setVisibleItems(1); // Show 1 item + More
        }
      } else {
        // Below md - mobile menu
        setVisibleItems(0);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [allNavItems.length]);

  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center flex-shrink-0">
            <img
              src="/images/logo.jpg"
              alt="Gesco Stay"
              className="h-10 sm:h-12 w-auto"
            />
          </Link>

          {/* Responsive Navigation (md and above) - Compact style for all screen sizes */}
          <nav ref={navRef} className="hidden md:flex items-center space-x-1">
            {/* Show only items that fit based on screen width */}
            {allNavItems.slice(0, visibleItems).map((item) => {
              if (item.external) {
                return (
                  <a
                    key={item.to}
                    href={item.to}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-600 hover:text-gray-900 text-sm whitespace-nowrap px-3 py-1.5 rounded hover:bg-gray-100 transition-colors"
                  >
                    {item.label}
                  </a>
                );
              } else {
                return (
                  <Link
                    key={item.to}
                    to={item.to}
                    className="text-gray-600 hover:text-gray-900 text-sm whitespace-nowrap px-3 py-1.5 rounded hover:bg-gray-100 transition-colors"
                  >
                    {item.label}
                  </Link>
                );
              }
            })}

            {/* More dropdown for remaining items */}
            {visibleItems < allNavItems.length && allNavItems.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-gray-900 text-sm whitespace-nowrap px-2 py-1"
                  >
                    More <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="w-48 max-h-96 overflow-y-auto"
                >
                  {allNavItems.slice(visibleItems).map((item) => (
                    <DropdownMenuItem key={item.to} asChild>
                      {item.external ? (
                        <a
                          href={item.to}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-full cursor-pointer"
                        >
                          {item.label}
                        </a>
                      ) : (
                        <Link to={item.to} className="w-full cursor-pointer">
                          {item.label}
                        </Link>
                      )}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </nav>

          {/* User Menu (md and above) - Compact style */}
          <div className="hidden md:flex items-center space-x-2">
            {user && (
              <Link
                to="/messages"
                className="text-gray-600 hover:text-gray-900 p-2 rounded-full hover:bg-gray-100"
                title="Messages"
              >
                <MessageCircle className="h-5 w-5" />
              </Link>
            )}
            <NavbarUserMenu />
          </div>

          {/* Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="sm">
                {isOpen ? <X /> : <Menu />}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[250px] sm:w-[300px]">
              <nav className="flex flex-col gap-4 mt-8">
                <SheetClose asChild>
                  <Link
                    to="/listings"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Properties
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    to="/hotels"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Hotels
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    to="/cars"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Car Rentals
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <a
                    href="https://gescogm.com/#/produits/categorie/2840"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Restaurants
                  </a>
                </SheetClose>
                <SheetClose asChild>
                  <a
                    href="http://e-tickets.online"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    Night Life
                  </a>
                </SheetClose>
                <SheetClose asChild>
                  <a
                    href="/#about"
                    className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                  >
                    About
                  </a>
                </SheetClose>
                {user && (
                  <>
                    <SheetClose asChild>
                      <Link
                        to="/messages"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        Messages
                      </Link>
                    </SheetClose>
                    <SheetClose asChild>
                      <Link
                        to="/listings/create"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        List Property
                      </Link>
                    </SheetClose>
                    <SheetClose asChild>
                      <Link
                        to="/hotels/create"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        List Hotel
                      </Link>
                    </SheetClose>
                    <SheetClose asChild>
                      <Link
                        to="/cars/create"
                        className="block py-2 px-4 hover:bg-gray-100 rounded-md"
                      >
                        List Car
                      </Link>
                    </SheetClose>
                  </>
                )}
                <div className="border-t my-4"></div>
                <div className="px-4">
                  <NavbarUserMenu />
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
