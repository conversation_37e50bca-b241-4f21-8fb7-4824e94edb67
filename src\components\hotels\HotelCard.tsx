import { Link } from "react-router-dom";
import { MapPin, Star, Clock, Users, TestTube } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface HotelCardProps {
  hotel: {
    id: string;
    title: string;
    location: string;
    rating?: number;
    reviews?: number;
    image?: string;
    images?: string[];
    check_in_time: string;
    check_out_time: string;
    amenities?: string[];
    min_price?: number;
    isSuperHost?: boolean;
    is_dummy?: boolean;
  };
}

const HotelCard = ({ hotel }: HotelCardProps) => {
  const {
    id,
    title,
    location,
    rating,
    reviews,
    image,
    images,
    check_in_time,
    check_out_time,
    amenities,
    min_price,
    isSuperHost = false,
  } = hotel;

  const displayImage =
    image || (images && images.length > 0 ? images[0] : "/placeholder.svg");
  const displayRating = rating || 0;
  const displayReviews = reviews || 0;

  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    } catch {
      return time;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <Link to={`/hotels/${id}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-shadow hover:shadow-md h-full flex flex-col">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden">
          <img
            src={displayImage}
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          {isSuperHost && (
            <div className="absolute top-2 left-2 px-2 py-1 bg-white text-xs font-medium rounded-full shadow-sm">
              Superhost
            </div>
          )}
          {hotel.is_dummy && (
            <div className="absolute top-2 left-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full shadow-sm flex items-center gap-1">
              <TestTube className="w-3 h-3" />
              Display Only
            </div>
          )}
          {rating && rating > 0 && (
            <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded-md flex items-center gap-1">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <span className="text-xs font-medium">{rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4 flex-1 flex flex-col">
          {/* Header */}
          <div className="mb-2">
            <h3 className="font-semibold text-gray-900 group-hover:text-accent transition-colors line-clamp-1">
              {title}
            </h3>
            <div className="flex items-center text-gray-500 text-sm mt-1">
              <MapPin className="w-4 h-4 mr-1" />
              <span className="line-clamp-1">{location}</span>
            </div>
          </div>

          {/* Check-in/Check-out times */}
          <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>Check-in: {formatTime(check_in_time)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>Check-out: {formatTime(check_out_time)}</span>
            </div>
          </div>

          {/* Amenities */}
          <div className="mb-3 flex-1">
            {amenities && amenities.length > 0 ? (
              <div className="flex flex-wrap gap-1">
                {amenities.slice(0, 3).map((amenity) => (
                  <Badge key={amenity} variant="secondary" className="text-xs">
                    {amenity}
                  </Badge>
                ))}
                {amenities.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{amenities.length - 3} more
                  </Badge>
                )}
              </div>
            ) : (
              <div className="text-xs text-gray-400">No amenities listed</div>
            )}
          </div>

          {/* Rating and Price */}
          <div className="flex items-center justify-between mt-auto">
            <div className="flex items-center">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
              <span className="text-sm font-medium text-gray-900">
                {displayRating.toFixed(1)}
              </span>
              <span className="text-sm text-gray-500 ml-1">
                ({displayReviews} reviews)
              </span>
            </div>

            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                ${min_price || 0}
              </div>
              <div className="text-sm text-gray-500">per night</div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default HotelCard;
