export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      bookings: {
        Row: {
          check_in: string;
          check_out: string;
          created_at: string | null;
          id: string;
          payment_id: string | null;
          payment_method: string | null;
          payment_status: string | null;
          property_id: string | null;
          status: string;
          stripe_session_id: string | null;
          total_price: number;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          check_in: string;
          check_out: string;
          created_at?: string | null;
          id?: string;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_status?: string | null;
          property_id?: string | null;
          status?: string;
          stripe_session_id?: string | null;
          total_price: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          check_in?: string;
          check_out?: string;
          created_at?: string | null;
          id?: string;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_status?: string | null;
          property_id?: string | null;
          status?: string;
          stripe_session_id?: string | null;
          total_price?: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "bookings_property_id_fkey";
            columns: ["property_id"];
            isOneToOne: false;
            referencedRelation: "popular_properties";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "bookings_property_id_fkey";
            columns: ["property_id"];
            isOneToOne: false;
            referencedRelation: "properties";
            referencedColumns: ["id"];
          }
        ];
      };
      car_bookings: {
        Row: {
          car_id: string;
          created_at: string;
          duration_type: Database["public"]["Enums"]["rental_duration"];
          end_date: string;
          id: string;
          start_date: string;
          status: string;
          total_price: number;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          car_id: string;
          created_at?: string;
          duration_type: Database["public"]["Enums"]["rental_duration"];
          end_date: string;
          id?: string;
          start_date: string;
          status?: string;
          total_price: number;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          car_id?: string;
          created_at?: string;
          duration_type?: Database["public"]["Enums"]["rental_duration"];
          end_date?: string;
          id?: string;
          start_date?: string;
          status?: string;
          total_price?: number;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "car_bookings_car_id_fkey";
            columns: ["car_id"];
            isOneToOne: false;
            referencedRelation: "cars";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "car_bookings_car_id_fkey";
            columns: ["car_id"];
            isOneToOne: false;
            referencedRelation: "popular_cars";
            referencedColumns: ["id"];
          }
        ];
      };
      car_insurance_options: {
        Row: {
          coverage_details: Json;
          created_at: string;
          description: string;
          id: string;
          name: string;
          price_day: number;
          updated_at: string;
        };
        Insert: {
          coverage_details: Json;
          created_at?: string;
          description: string;
          id?: string;
          name: string;
          price_day: number;
          updated_at?: string;
        };
        Update: {
          coverage_details?: Json;
          created_at?: string;
          description?: string;
          id?: string;
          name?: string;
          price_day?: number;
          updated_at?: string;
        };
        Relationships: [];
      };
      cars: {
        Row: {
          car_type: Database["public"]["Enums"]["car_type"];
          created_at: string;
          description: string;
          features: string[];
          formatted_address: string | null;
          fuel_type: string;
          id: string;
          images: string[];
          is_dummy: boolean | null;
          latitude: number | null;
          location: string;
          longitude: number | null;
          make: string;
          model: string;
          owner_id: string;
          price_day: number;
          price_month: number;
          price_week: number;
          seats: number;
          status: Database["public"]["Enums"]["listing_status"];
          title: string;
          transmission: string;
          updated_at: string;
          year: number;
        };
        Insert: {
          car_type: Database["public"]["Enums"]["car_type"];
          created_at?: string;
          description: string;
          features?: string[];
          formatted_address?: string | null;
          fuel_type: string;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location: string;
          longitude?: number | null;
          make: string;
          model: string;
          owner_id: string;
          price_day: number;
          price_month: number;
          price_week: number;
          seats: number;
          status?: Database["public"]["Enums"]["listing_status"];
          title: string;
          transmission: string;
          updated_at?: string;
          year: number;
        };
        Update: {
          car_type?: Database["public"]["Enums"]["car_type"];
          created_at?: string;
          description?: string;
          features?: string[];
          formatted_address?: string | null;
          fuel_type?: string;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location?: string;
          longitude?: number | null;
          make?: string;
          model?: string;
          owner_id?: string;
          price_day?: number;
          price_month?: number;
          price_week?: number;
          seats?: number;
          status?: Database["public"]["Enums"]["listing_status"];
          title?: string;
          transmission?: string;
          updated_at?: string;
          year?: number;
        };
        Relationships: [];
      };
      host_payment_methods: {
        Row: {
          account_id: string;
          created_at: string;
          host_id: string;
          id: string;
          is_default: boolean;
          provider: string;
          status: string;
          updated_at: string;
        };
        Insert: {
          account_id: string;
          created_at?: string;
          host_id: string;
          id?: string;
          is_default?: boolean;
          provider: string;
          status?: string;
          updated_at?: string;
        };
        Update: {
          account_id?: string;
          created_at?: string;
          host_id?: string;
          id?: string;
          is_default?: boolean;
          provider?: string;
          status?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      payment_logs: {
        Row: {
          amount: number;
          booking_id: string | null;
          created_at: string | null;
          id: string;
          payment_method: string;
          provider_response: Json | null;
          status: string;
          transaction_id: string | null;
        };
        Insert: {
          amount: number;
          booking_id?: string | null;
          created_at?: string | null;
          id?: string;
          payment_method: string;
          provider_response?: Json | null;
          status: string;
          transaction_id?: string | null;
        };
        Update: {
          amount?: number;
          booking_id?: string | null;
          created_at?: string | null;
          id?: string;
          payment_method?: string;
          provider_response?: Json | null;
          status?: string;
          transaction_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "payment_logs_booking_id_fkey";
            columns: ["booking_id"];
            isOneToOne: false;
            referencedRelation: "bookings";
            referencedColumns: ["id"];
          }
        ];
      };
      payout_requests: {
        Row: {
          admin_notes: string | null;
          amount: number;
          created_at: string;
          host_id: string;
          id: string;
          notes: string | null;
          payment_method_id: string | null;
          processed_at: string | null;
          processed_by: string | null;
          status: string;
          updated_at: string;
        };
        Insert: {
          admin_notes?: string | null;
          amount: number;
          created_at?: string;
          host_id: string;
          id?: string;
          notes?: string | null;
          payment_method_id?: string | null;
          processed_at?: string | null;
          processed_by?: string | null;
          status?: string;
          updated_at?: string;
        };
        Update: {
          admin_notes?: string | null;
          amount?: number;
          created_at?: string;
          host_id?: string;
          id?: string;
          notes?: string | null;
          payment_method_id?: string | null;
          processed_at?: string | null;
          processed_by?: string | null;
          status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "payout_requests_payment_method_id_fkey";
            columns: ["payment_method_id"];
            isOneToOne: false;
            referencedRelation: "host_payment_methods";
            referencedColumns: ["id"];
          }
        ];
      };
      platform_earnings: {
        Row: {
          booking_id: string | null;
          booking_type: string;
          car_booking_id: string | null;
          created_at: string;
          host_payout_amount: number;
          hotel_booking_id: string | null;
          id: string;
          platform_fee: number;
          total_booking_amount: number;
        };
        Insert: {
          booking_id?: string | null;
          booking_type: string;
          car_booking_id?: string | null;
          created_at?: string;
          host_payout_amount: number;
          hotel_booking_id?: string | null;
          id?: string;
          platform_fee: number;
          total_booking_amount: number;
        };
        Update: {
          booking_id?: string | null;
          booking_type?: string;
          car_booking_id?: string | null;
          created_at?: string;
          host_payout_amount?: number;
          hotel_booking_id?: string | null;
          id?: string;
          platform_fee?: number;
          total_booking_amount?: number;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string;
          email: string | null;
          first_name: string | null;
          id: string;
          last_name: string | null;
          phone_number: string | null;
          role: Database["public"]["Enums"]["user_role"];
          roles: string[] | null;
          updated_at: string;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          id: string;
          last_name?: string | null;
          phone_number?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          roles?: string[] | null;
          updated_at?: string;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          id?: string;
          last_name?: string | null;
          phone_number?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          roles?: string[] | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      properties: {
        Row: {
          baths: number;
          beds: number;
          created_at: string;
          description: string;
          features: string[];
          formatted_address: string | null;
          id: string;
          images: string[];
          is_dummy: boolean | null;
          latitude: number | null;
          location: string;
          longitude: number | null;
          owner_id: string;
          price: number;
          status: Database["public"]["Enums"]["listing_status"];
          title: string;
          updated_at: string;
        };
        Insert: {
          baths: number;
          beds: number;
          created_at?: string;
          description: string;
          features?: string[];
          formatted_address?: string | null;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location: string;
          longitude?: number | null;
          owner_id: string;
          price: number;
          status?: Database["public"]["Enums"]["listing_status"];
          title: string;
          updated_at?: string;
        };
        Update: {
          baths?: number;
          beds?: number;
          created_at?: string;
          description?: string;
          features?: string[];
          formatted_address?: string | null;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location?: string;
          longitude?: number | null;
          owner_id?: string;
          price?: number;
          status?: Database["public"]["Enums"]["listing_status"];
          title?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      hotels: {
        Row: {
          amenities: string[];
          check_in_time: string;
          check_out_time: string;
          created_at: string;
          description: string;
          formatted_address: string | null;
          id: string;
          images: string[];
          is_dummy: boolean | null;
          latitude: number | null;
          location: string;
          longitude: number | null;
          owner_id: string;
          policies: Json;
          status: Database["public"]["Enums"]["listing_status"];
          title: string;
          updated_at: string;
        };
        Insert: {
          amenities?: string[];
          check_in_time?: string;
          check_out_time?: string;
          created_at?: string;
          description: string;
          formatted_address?: string | null;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location: string;
          longitude?: number | null;
          owner_id: string;
          policies?: Json;
          status?: Database["public"]["Enums"]["listing_status"];
          title: string;
          updated_at?: string;
        };
        Update: {
          amenities?: string[];
          check_in_time?: string;
          check_out_time?: string;
          created_at?: string;
          description?: string;
          formatted_address?: string | null;
          id?: string;
          images?: string[];
          is_dummy?: boolean | null;
          latitude?: number | null;
          location?: string;
          longitude?: number | null;
          owner_id?: string;
          policies?: Json;
          status?: Database["public"]["Enums"]["listing_status"];
          title?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      hotel_bookings: {
        Row: {
          adults: number;
          check_in: string;
          check_out: string;
          children: number;
          created_at: string;
          guest_details: Json;
          hotel_id: string;
          id: string;
          payment_id: string | null;
          payment_method: string | null;
          payment_status: string;
          room_type_id: string | null;
          rooms_count: number | null;
          special_requests: string | null;
          status: string;
          stripe_session_id: string | null;
          total_price: number;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          adults?: number;
          check_in: string;
          check_out: string;
          children?: number;
          created_at?: string;
          guest_details?: Json;
          hotel_id: string;
          id?: string;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          room_type_id?: string | null;
          rooms_count?: number | null;
          special_requests?: string | null;
          status?: string;
          stripe_session_id?: string | null;
          total_price: number;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          adults?: number;
          check_in?: string;
          check_out?: string;
          children?: number;
          created_at?: string;
          guest_details?: Json;
          hotel_id?: string;
          id?: string;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          room_type_id?: string | null;
          rooms_count?: number | null;
          special_requests?: string | null;
          status?: string;
          stripe_session_id?: string | null;
          total_price?: number;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "hotel_bookings_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "hotel_bookings_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "popular_hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "hotel_bookings_room_type_id_fkey";
            columns: ["room_type_id"];
            isOneToOne: false;
            referencedRelation: "room_types";
            referencedColumns: ["id"];
          }
        ];
      };
      room_assignments: {
        Row: {
          assigned_at: string;
          hotel_booking_id: string;
          id: string;
          room_inventory_id: string;
        };
        Insert: {
          assigned_at?: string;
          hotel_booking_id: string;
          id?: string;
          room_inventory_id: string;
        };
        Update: {
          assigned_at?: string;
          hotel_booking_id?: string;
          id?: string;
          room_inventory_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "room_assignments_hotel_booking_id_fkey";
            columns: ["hotel_booking_id"];
            isOneToOne: false;
            referencedRelation: "hotel_bookings";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "room_assignments_room_inventory_id_fkey";
            columns: ["room_inventory_id"];
            isOneToOne: false;
            referencedRelation: "room_inventory";
            referencedColumns: ["id"];
          }
        ];
      };
      room_inventory: {
        Row: {
          created_at: string;
          floor_number: number | null;
          id: string;
          room_number: string;
          room_type_id: string;
          status: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          floor_number?: number | null;
          id?: string;
          room_number: string;
          room_type_id: string;
          status?: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          floor_number?: number | null;
          id?: string;
          room_number?: string;
          room_type_id?: string;
          status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "room_inventory_room_type_id_fkey";
            columns: ["room_type_id"];
            isOneToOne: false;
            referencedRelation: "room_types";
            referencedColumns: ["id"];
          }
        ];
      };
      room_types: {
        Row: {
          amenities: string[];
          base_price: number;
          bed_configuration: string | null;
          created_at: string;
          description: string | null;
          hotel_id: string;
          id: string;
          images: string[];
          max_occupancy: number;
          name: string;
          room_size_sqm: number | null;
          updated_at: string;
        };
        Insert: {
          amenities?: string[];
          base_price: number;
          bed_configuration?: string | null;
          created_at?: string;
          description?: string | null;
          hotel_id: string;
          id?: string;
          images?: string[];
          max_occupancy: number;
          name: string;
          room_size_sqm?: number | null;
          updated_at?: string;
        };
        Update: {
          amenities?: string[];
          base_price?: number;
          bed_configuration?: string | null;
          created_at?: string;
          description?: string | null;
          hotel_id?: string;
          id?: string;
          images?: string[];
          max_occupancy?: number;
          name?: string;
          room_size_sqm?: number | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "room_types_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "room_types_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "popular_hotels";
            referencedColumns: ["id"];
          }
        ];
      };
      seasonal_pricing: {
        Row: {
          created_at: string;
          description: string | null;
          end_date: string;
          id: string;
          price_multiplier: number;
          room_type_id: string;
          start_date: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          end_date: string;
          id?: string;
          price_multiplier?: number;
          room_type_id: string;
          start_date: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          end_date?: string;
          id?: string;
          price_multiplier?: number;
          room_type_id?: string;
          start_date?: string;
        };
        Relationships: [
          {
            foreignKeyName: "seasonal_pricing_room_type_id_fkey";
            columns: ["room_type_id"];
            isOneToOne: false;
            referencedRelation: "room_types";
            referencedColumns: ["id"];
          }
        ];
      };
      reviews: {
        Row: {
          car_id: string | null;
          comment: string | null;
          created_at: string | null;
          hotel_id: string | null;
          id: string;
          property_id: string | null;
          rating: number;
          user_id: string | null;
        };
        Insert: {
          car_id?: string | null;
          comment?: string | null;
          created_at?: string | null;
          hotel_id?: string | null;
          id?: string;
          property_id?: string | null;
          rating: number;
          user_id?: string | null;
        };
        Update: {
          car_id?: string | null;
          comment?: string | null;
          created_at?: string | null;
          hotel_id?: string | null;
          id?: string;
          property_id?: string | null;
          rating?: number;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "reviews_car_id_fkey";
            columns: ["car_id"];
            isOneToOne: false;
            referencedRelation: "cars";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "reviews_car_id_fkey";
            columns: ["car_id"];
            isOneToOne: false;
            referencedRelation: "popular_cars";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "reviews_property_id_fkey";
            columns: ["property_id"];
            isOneToOne: false;
            referencedRelation: "popular_properties";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "reviews_property_id_fkey";
            columns: ["property_id"];
            isOneToOne: false;
            referencedRelation: "properties";
            referencedColumns: ["id"];
          }
        ];
      };
      conversations: {
        Row: {
          id: string;
          participant_1_id: string;
          participant_2_id: string;
          property_id: string | null;
          car_id: string | null;
          hotel_id: string | null;
          created_at: string;
          updated_at: string;
          last_message_at: string | null;
        };
        Insert: {
          id?: string;
          participant_1_id: string;
          participant_2_id: string;
          property_id?: string | null;
          car_id?: string | null;
          hotel_id?: string | null;
          created_at?: string;
          updated_at?: string;
          last_message_at?: string | null;
        };
        Update: {
          id?: string;
          participant_1_id?: string;
          participant_2_id?: string;
          property_id?: string | null;
          car_id?: string | null;
          hotel_id?: string | null;
          created_at?: string;
          updated_at?: string;
          last_message_at?: string | null;
        };
        Relationships: [];
      };
      messages: {
        Row: {
          id: string;
          conversation_id: string;
          sender_id: string;
          content: string;
          created_at: string;
          read_at: string | null;
        };
        Insert: {
          id?: string;
          conversation_id: string;
          sender_id: string;
          content: string;
          created_at?: string;
          read_at?: string | null;
        };
        Update: {
          id?: string;
          conversation_id?: string;
          sender_id?: string;
          content?: string;
          created_at?: string;
          read_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey";
            columns: ["conversation_id"];
            isOneToOne: false;
            referencedRelation: "conversations";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      booking_analytics: {
        Row: {
          cancelled_bookings: number | null;
          confirmed_bookings: number | null;
          month: string | null;
          total_bookings: number | null;
          total_revenue: number | null;
        };
        Relationships: [];
      };
      car_booking_analytics: {
        Row: {
          cancelled_bookings: number | null;
          confirmed_bookings: number | null;
          month: string | null;
          total_bookings: number | null;
          total_revenue: number | null;
        };
        Relationships: [];
      };
      popular_cars: {
        Row: {
          avg_rating: number | null;
          booking_count: number | null;
          id: string | null;
          location: string | null;
          make: string | null;
          model: string | null;
          title: string | null;
          total_revenue: number | null;
        };
        Relationships: [];
      };
      popular_properties: {
        Row: {
          avg_rating: number | null;
          booking_count: number | null;
          id: string | null;
          location: string | null;
          title: string | null;
          total_revenue: number | null;
        };
        Relationships: [];
      };
      popular_hotels: {
        Row: {
          amenities: string[] | null;
          avg_rating: number | null;
          booking_count: number | null;
          check_in_time: string | null;
          check_out_time: string | null;
          created_at: string | null;
          description: string | null;
          formatted_address: string | null;
          id: string | null;
          images: string[] | null;
          latitude: number | null;
          location: string | null;
          longitude: number | null;
          owner_id: string | null;
          policies: Json | null;
          review_count: number | null;
          status: Database["public"]["Enums"]["listing_status"] | null;
          title: string | null;
          updated_at: string | null;
        };
        Relationships: [];
      };
    };
    Functions: {
      calculate_platform_fee: {
        Args: { booking_amount: number; booking_type?: string };
        Returns: number;
      };
      check_car_availability: {
        Args: { car_id: string; start_date: string; end_date: string };
        Returns: boolean;
      };
      check_property_availability: {
        Args: {
          property_id: string;
          check_in_date: string;
          check_out_date: string;
        };
        Returns: boolean;
      };
      check_room_availability: {
        Args: {
          p_room_type_id: string;
          p_check_in: string;
          p_check_out: string;
          p_rooms_needed?: number;
        };
        Returns: number;
      };
      get_first_user_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      get_host_payout_requests: {
        Args: { host_id: string };
        Returns: {
          id: string;
          host_id: string;
          amount: number;
          status: string;
          notes: string;
          admin_notes: string;
          created_at: string;
          updated_at: string;
          payment_method_id: string;
          processed_at: string;
          processed_by: string;
          payment_method: Json;
        }[];
      };
      get_payout_requests_with_details: {
        Args: { admin_id: string };
        Returns: {
          id: string;
          host_id: string;
          amount: number;
          status: string;
          notes: string;
          admin_notes: string;
          created_at: string;
          updated_at: string;
          payment_method_id: string;
          processed_at: string;
          processed_by: string;
          payment_method: Json;
          host_details: Json;
        }[];
      };
      is_admin: {
        Args: { uid: string };
        Returns: boolean;
      };
      get_or_create_conversation: {
        Args: {
          other_user_id: string;
          property_id_param?: string;
          car_id_param?: string;
          hotel_id_param?: string;
        };
        Returns: string;
      };
      send_message: {
        Args: {
          conversation_id_param: string;
          content_param: string;
        };
        Returns: string;
      };
      mark_messages_as_read: {
        Args: {
          conversation_id_param: string;
        };
        Returns: number;
      };
      get_user_conversations: {
        Args: Record<PropertyKey, never>;
        Returns: {
          id: string;
          other_user_id: string;
          other_user_name: string;
          other_user_avatar: string;
          property_id: string;
          property_title: string;
          car_id: string;
          car_title: string;
          hotel_id: string;
          hotel_title: string;
          last_message_content: string;
          last_message_at: string;
          last_message_sender_id: string;
          unread_count: number;
          created_at: string;
        }[];
      };
    };
    Enums: {
      car_type:
        | "sedan"
        | "suv"
        | "luxury"
        | "compact"
        | "convertible"
        | "van"
        | "truck";
      listing_status: "pending" | "approved" | "rejected";
      rental_duration: "day" | "week" | "month";
      user_role: "guest" | "host" | "admin";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      car_type: [
        "sedan",
        "suv",
        "luxury",
        "compact",
        "convertible",
        "van",
        "truck",
      ],
      listing_status: ["pending", "approved", "rejected"],
      rental_duration: ["day", "week", "month"],
      user_role: ["guest", "host", "admin"],
    },
  },
} as const;
