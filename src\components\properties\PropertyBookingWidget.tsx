import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Star, CreditCard, Wallet, TestTube } from "lucide-react";
import { useNavigate, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { saveBookingIntent } from "@/utils/bookingContinuation";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { usePropertyBookedDates } from "@/hooks/useBookedDates";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ContactHostButton from "@/components/messaging/ContactHostButton";

interface PropertyBookingWidgetProps {
  price: number;
  rating: number;
  propertyId: string;
  propertyTitle?: string;
  hostId?: string;
  isDummy?: boolean;
}

const PropertyBookingWidget = ({
  price,
  rating,
  propertyId,
  propertyTitle,
  hostId,
  isDummy = false,
}: PropertyBookingWidgetProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [isBookingMode, setIsBookingMode] = useState(false);
  const [checkInDate, setCheckInDate] = useState<Date | undefined>(undefined);
  const [checkOutDate, setCheckOutDate] = useState<Date | undefined>(undefined);
  const [guests, setGuests] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [totalAmount, setTotalAmount] = useState(0);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  // Fetch booked dates for this property
  const { data: bookedDates = [] } = usePropertyBookedDates(propertyId);

  const handleAuthRequired = () => {
    // Save booking intent before redirecting to auth
    saveBookingIntent({
      type: "property",
      id: propertyId,
      checkIn: checkInDate?.toISOString().split("T")[0],
      checkOut: checkOutDate?.toISOString().split("T")[0],
      guests,
      totalPrice: totalAmount,
      returnUrl: window.location.pathname,
    });

    // Navigate to auth page
    navigate("/auth");
  };

  const handleBooking = async () => {
    if (isDummy) {
      toast({
        title: "Cannot book test listing",
        description:
          "This is a test listing and cannot be booked. Please choose a real listing.",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      handleAuthRequired();
      return;
    }

    if (!checkInDate || !checkOutDate) {
      toast({
        title: "Please select check-in and check-out dates",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Calculate number of days
      const days = Math.ceil(
        (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Calculate total price (base price * days)
      const amount = price * days;
      setTotalAmount(amount);

      // Check property availability
      const { data: isAvailable } = await supabase.rpc(
        "check_property_availability",
        {
          property_id: propertyId,
          check_in_date: checkInDate.toISOString().split("T")[0],
          check_out_date: checkOutDate.toISOString().split("T")[0],
        }
      );

      if (!isAvailable) {
        toast({
          title: "Property not available",
          description: "This property is not available for the selected dates",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Don't create booking record yet - only show payment dialog
      // Booking will be created when payment is initiated
      setShowPaymentDialog(true);
    } catch (error: any) {
      console.error("Booking error:", error);
      toast({
        title: "Booking failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStripePayment = async () => {
    if (!user?.email || !checkInDate || !checkOutDate) return;

    setIsLoading(true);
    try {
      // Create booking record first
      const { data: booking, error: bookingError } = await supabase
        .from("bookings")
        .insert({
          property_id: propertyId,
          user_id: user.id,
          check_in: checkInDate.toISOString().split("T")[0],
          check_out: checkOutDate.toISOString().split("T")[0],
          total_price: totalAmount,
          status: "pending", // Only mark as confirmed after successful payment
          payment_status: "pending",
        })
        .select("id")
        .single();

      if (bookingError) throw bookingError;

      const { data, error } = await supabase.functions.invoke(
        "process-stripe-payment",
        {
          body: {
            bookingId: booking.id,
            propertyId,
            amount: totalAmount,
            returnUrl: window.location.origin,
            userEmail: user.email,
          },
        }
      );

      if (error) throw error;

      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      } else {
        throw new Error("No session URL returned");
      }
    } catch (error: any) {
      console.error("Payment error:", error);
      toast({
        title: "Payment failed",
        description: error.message,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const handleWavePayment = async () => {
    if (!user?.email || !checkInDate || !checkOutDate) return;

    setIsLoading(true);
    try {
      // Create booking record first
      const { data: booking, error: bookingError } = await supabase
        .from("bookings")
        .insert({
          property_id: propertyId,
          user_id: user.id,
          check_in: checkInDate.toISOString().split("T")[0],
          check_out: checkOutDate.toISOString().split("T")[0],
          total_price: totalAmount,
          status: "pending", // Only mark as confirmed after successful payment
          payment_status: "pending",
        })
        .select("id")
        .single();

      if (bookingError) throw bookingError;

      // Use supabase.functions.invoke to call the process-wave-payment edge function (cloud)
      const { data: result, error } = await supabase.functions.invoke(
        "process-wave-payment",
        {
          body: {
            bookingId: booking.id,
            bookingType: "property",
            amount: totalAmount,
            returnUrl: window.location.origin,
            userEmail: user.email,
            userMobile: user.phone || "",
          },
        }
      );

      if (error) {
        toast({
          title: "Wave payment failed",
          description: error.message || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      } else if (result?.success && result?.checkoutUrl) {
        // Check if the URL is a deep link (wave://) and handle accordingly
        if (result.checkoutUrl.startsWith("wave://")) {
          // For deep links, create a custom payment page
          const paymentUrl = `/wave-payment?checkout_id=${result.checkoutId}&booking_id=${booking.id}&amount=${totalAmount}`;
          window.location.href = paymentUrl;
        } else {
          // For regular URLs, redirect directly
          window.location.href = result.checkoutUrl;
        }
      } else {
        toast({
          title: "Wave payment failed",
          description: result?.error || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      const err = error as Error;
      console.error("Wave payment error:", err);
      toast({
        title: "Payment failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotalPrice = () => {
    if (!checkInDate || !checkOutDate) return 0;

    const days = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    return price * days;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
      {isDummy && (
        <div className="mb-4">
          <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1 w-fit">
            <TestTube className="w-3 h-3" />
            For Display Only
          </Badge>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <div>
          <span className="text-2xl font-bold">${price}</span>
          <span className="text-gray-600"> night</span>
        </div>
        <div className="flex items-center">
          <Star className="w-4 h-4 text-yellow-500 mr-1" />
          <span>{rating}</span>
        </div>
      </div>

      {isBookingMode ? (
        <div>
          <div className="border border-gray-200 rounded-lg p-4 mb-4">
            <div className="mb-4">
              <DateRangePicker
                checkInDate={checkInDate}
                checkOutDate={checkOutDate}
                onCheckInChange={setCheckInDate}
                onCheckOutChange={setCheckOutDate}
                disabledDates={bookedDates}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guests
              </label>
              <select
                value={guests}
                onChange={(e) => setGuests(Number(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              >
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <option key={num} value={num}>
                    {num} {num === 1 ? "guest" : "guests"}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Button
            onClick={handleBooking}
            className={`w-full bg-secondary hover:bg-secondary/90 text-white mb-4 ${
              isDummy ? "cursor-not-allowed opacity-50" : ""
            }`}
            disabled={isDummy || !checkInDate || !checkOutDate || isLoading}
          >
            {isDummy
              ? "Test Listing - Cannot Book"
              : isLoading
              ? "Processing..."
              : "Reserve"}
          </Button>

          <Button
            variant="outline"
            onClick={() => setIsBookingMode(false)}
            className="w-full"
            disabled={isLoading}
          >
            Cancel
          </Button>

          {checkInDate && checkOutDate && (
            <div className="mt-4 border-t pt-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 underline">
                  ${price} x{" "}
                  {Math.ceil(
                    (checkOutDate.getTime() - checkInDate.getTime()) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  nights
                </span>
                <span>
                  $
                  {price *
                    Math.ceil(
                      (checkOutDate.getTime() - checkInDate.getTime()) /
                        (1000 * 60 * 60 * 24)
                    )}
                </span>
              </div>
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total</span>
                <span>${calculateTotalPrice()}</span>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          <Button
            onClick={() => setIsBookingMode(true)}
            className="w-full bg-accent hover:bg-accent/90 text-white"
          >
            Check availability
          </Button>

          {hostId && (
            <ContactHostButton
              hostId={hostId}
              propertyId={propertyId}
              propertyTitle={propertyTitle}
              variant="outline"
              className="w-full"
            />
          )}
        </div>
      )}

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Payment Method</DialogTitle>
            <DialogDescription>
              Choose how you'd like to pay for your booking
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="card" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="card">Credit Card</TabsTrigger>
              <TabsTrigger value="wave">Wave</TabsTrigger>
            </TabsList>

            <TabsContent value="card" className="mt-4">
              <div className="flex flex-col space-y-4">
                <div className="flex items-center p-4 border rounded-md">
                  <CreditCard className="h-6 w-6 mr-2" />
                  <div>
                    <p className="font-medium">Pay with card</p>
                    <p className="text-sm text-muted-foreground">
                      Secure payment via Stripe
                    </p>
                  </div>
                </div>

                <Button
                  onClick={handleStripePayment}
                  disabled={isLoading || !agreeToTerms}
                  className="w-full"
                >
                  {isLoading ? "Processing..." : "Continue to Payment"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="wave" className="mt-4">
              <div className="flex flex-col space-y-4">
                <div className="flex items-center p-4 border rounded-md">
                  <Wallet className="h-6 w-6 mr-2" />
                  <div>
                    <p className="font-medium">Pay with Wave</p>
                    <p className="text-sm text-muted-foreground">
                      Mobile money payment
                    </p>
                  </div>
                </div>

                <Button
                  onClick={handleWavePayment}
                  disabled={isLoading || !agreeToTerms}
                  className="w-full"
                >
                  {isLoading ? "Processing..." : "Continue to Wave"}
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* Terms and Conditions Checkbox */}
          <div className="flex items-center space-x-2 mt-4">
            <Checkbox
              id="terms"
              checked={agreeToTerms}
              onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
            />
            <label
              htmlFor="terms"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I agree to the{" "}
              <Link to="/terms" className="text-accent hover:underline">
                Terms & Safety Policy
              </Link>
            </label>
          </div>

          <DialogFooter className="flex justify-between items-center bg-gray-50 p-4 border-t">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <p className="text-lg font-semibold">Total: ${totalAmount}</p>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyBookingWidget;
