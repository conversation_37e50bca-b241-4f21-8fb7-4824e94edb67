
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";

type ListingStatus = Database['public']['Enums']['listing_status'];

const HostListings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [properties, setProperties] = useState<any[]>([]);
  const [hotels, setHotels] = useState<any[]>([]);
  const [cars, setCars] = useState<any[]>([]);

  useEffect(() => {
    if (user) {
      fetchUserListings();
    }
  }, [user]);

  const fetchUserListings = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Fetch properties
      const { data: propertiesData, error: propertiesError } = await supabase
        .from("properties")
        .select("*")
        .eq("owner_id", user.id);
      
      if (propertiesError) throw propertiesError;
      setProperties(propertiesData || []);

      // Fetch hotels
      const { data: hotelsData, error: hotelsError } = await supabase
        .from("hotels")
        .select("*")
        .eq("owner_id", user.id);

      if (hotelsError) throw hotelsError;
      setHotels(hotelsData || []);

      // Fetch cars
      const { data: carsData, error: carsError } = await supabase
        .from("cars")
        .select("*")
        .eq("owner_id", user.id);

      if (carsError) throw carsError;
      setCars(carsData || []);
      
    } catch (error: any) {
      console.error("Error fetching listings:", error);
      toast.error("Failed to fetch your listings");
    } finally {
      setLoading(false);
    }
  };

  const updateListingStatus = async (id: string, table: "properties" | "hotels" | "cars", status: ListingStatus) => {
    try {
      const { error } = await supabase
        .from(table)
        .update({ status })
        .eq("id", id);
        
      if (error) throw error;
      
      toast.success(`Listing ${status === 'approved' ? 'activated' : 'pending'} successfully`);
      fetchUserListings();
    } catch (error: any) {
      console.error("Error updating listing status:", error);
      toast.error("Failed to update listing status");
    }
  };

  const deleteListing = async (id: string, table: "properties" | "hotels" | "cars") => {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq("id", id);
        
      if (error) throw error;
      
      toast.success("Listing deleted successfully");
      fetchUserListings();
    } catch (error: any) {
      console.error("Error deleting listing:", error);
      toast.error("Failed to delete listing");
    }
  };

  const renderListingCard = (listing: any, type: 'properties' | 'hotels' | 'cars') => {
    const isProperty = type === 'properties';
    const isHotel = type === 'hotels';
    const isCar = type === 'cars';
    const isActive = listing.status === 'approved';
    
    return (
      <Card key={listing.id} className="overflow-hidden">
        <div className="aspect-video relative">
          <img 
            src={listing.images?.[0] || '/placeholder.svg'} 
            alt={listing.title || `${listing.make} ${listing.model}`} 
            className="object-cover w-full h-full"
          />
          <Badge 
            variant={isActive ? 'default' : 'secondary'}
            className="absolute top-2 right-2"
          >
            {listing.status}
          </Badge>
        </div>
        <CardContent className="p-4">
          <h3 className="font-bold truncate">
            {isProperty || isHotel ? listing.title : `${listing.make} ${listing.model} (${listing.year})`}
          </h3>
          <p className="text-sm text-muted-foreground truncate">
            {listing.location}
          </p>
          <div className="mt-2">
            <span className="font-semibold">
              ${isProperty ? listing.price : isCar ? listing.price_day : 'Contact for rates'}/
              {isHotel ? 'night' : 'day'}
            </span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between p-4 pt-0">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              const table = isProperty ? 'properties' : isHotel ? 'hotels' : 'cars';
              return isActive
                ? updateListingStatus(listing.id, table, 'pending')
                : updateListingStatus(listing.id, table, 'approved');
            }}
          >
            {isActive ? 'Deactivate' : 'Activate'}
          </Button>
          <Button 
            variant="destructive" 
            size="sm"
            onClick={() => {
              if (window.confirm('Are you sure you want to delete this listing?')) {
                const table = isProperty ? 'properties' : isHotel ? 'hotels' : 'cars';
                deleteListing(listing.id, table);
              }
            }}
          >
            Delete
          </Button>
        </CardFooter>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Your Listings</h1>
        <div className="flex space-x-2">
          <Button onClick={() => navigate("/listings/create")}>
            Add Property
          </Button>
          <Button onClick={() => navigate("/hotels/create")} variant="outline">
            Add Hotel
          </Button>
          <Button onClick={() => navigate("/cars/create")} variant="outline">
            Add Car
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Listings</TabsTrigger>
          <TabsTrigger value="properties">Properties</TabsTrigger>
          <TabsTrigger value="hotels">Hotels</TabsTrigger>
          <TabsTrigger value="cars">Cars</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-4">
          {properties.length === 0 && hotels.length === 0 && cars.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">You don't have any listings yet.</p>
              <div className="flex justify-center space-x-2">
                <Button onClick={() => navigate("/listings/create")}>
                  Add Property
                </Button>
                <Button onClick={() => navigate("/hotels/create")} variant="outline">
                  Add Hotel
                </Button>
                <Button onClick={() => navigate("/cars/create")} variant="outline">
                  Add Car
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {properties.map(property => renderListingCard(property, 'properties'))}
              {hotels.map(hotel => renderListingCard(hotel, 'hotels'))}
              {cars.map(car => renderListingCard(car, 'cars'))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="properties" className="mt-4">
          {properties.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">You don't have any property listings yet.</p>
              <Button onClick={() => navigate("/listings/create")}>
                Add Property
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {properties.map(property => renderListingCard(property, 'properties'))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="hotels" className="mt-4">
          {hotels.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">You don't have any hotel listings yet.</p>
              <Button onClick={() => navigate("/hotels/create")} variant="outline">
                Add Hotel
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {hotels.map(hotel => renderListingCard(hotel, 'hotels'))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="cars" className="mt-4">
          {cars.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">You don't have any car listings yet.</p>
              <Button onClick={() => navigate("/cars/create")} variant="outline">
                Add Car
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {cars.map(car => renderListingCard(car, 'cars'))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HostListings;
