import { Search, MessageCircle, CreditCard, MapPin } from "lucide-react";
import { Link } from "react-router-dom";

const HowItWorksSection = () => {
  const steps = [
    {
      icon: <Search className="w-12 h-12 text-accent" />,
      title: "Search & Discover",
      description: "Browse through our curated selection of verified properties and car rentals across Africa.",
      step: "01"
    },
    {
      icon: <MessageCircle className="w-12 h-12 text-secondary" />,
      title: "Connect & Communicate",
      description: "Chat directly with hosts to ask questions, get local tips, and ensure your stay meets your needs.",
      step: "02"
    },
    {
      icon: <CreditCard className="w-12 h-12 text-accent" />,
      title: "Book Securely",
      description: "Make secure payments through our platform with full protection and instant confirmation.",
      step: "03"
    },
    {
      icon: <MapPin className="w-12 h-12 text-secondary" />,
      title: "Enjoy Your Stay",
      description: "Arrive at your destination with confidence, knowing everything is verified and ready for you.",
      step: "04"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            How Gescostay Works
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From discovery to departure, we've made booking your perfect stay simple, secure, and seamless
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              {/* Step connector line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-accent/30 to-secondary/30 transform translate-x-4 z-0"></div>
              )}
              
              <div className="relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-100 z-10 h-full flex flex-col">
                {/* Step number */}
                <div className="absolute -top-4 -right-4 w-12 h-12 bg-gradient-to-r from-accent to-secondary rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                  {step.step}
                </div>

                <div className="flex justify-center mb-6">
                  <div className="p-4 bg-gray-50 rounded-full">
                    {step.icon}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                  {step.title}
                </h3>

                <p className="text-gray-600 text-center leading-relaxed flex-grow">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-accent/10 via-secondary/10 to-accent/10 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Your Journey?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of travelers who have discovered the beauty of Africa through Gescostay
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link to="/listings" className="bg-gradient-to-r from-accent to-secondary hover:from-accent/90 hover:to-secondary/90 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-block text-center">
                Browse Properties
              </Link>
              <Link to="/listings/create" className="border-2 border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 inline-block text-center">
                List Your Property
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
