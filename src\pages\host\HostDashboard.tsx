import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useNavigate } from "react-router-dom";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  BarChart,
  Home,
  MessageSquare,
  CalendarDays,
  DollarSign,
} from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";

const HostDashboard = () => {
  const { user, getUserRole, getUserRoles } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [properties, setProperties] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [roleCheckComplete, setRoleCheckComplete] = useState(false);

  useEffect(() => {
    console.log("HostDashboard component mounted/re-rendered");

    const checkUserRole = async () => {
      if (!user) {
        console.log("HostDashboard: No user found, redirecting to auth");
        navigate("/auth");
        return;
      }

      try {
        // Add a small delay to ensure auth state is fully loaded
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Use AuthProvider's getUserRoles method for consistency
        const roles = await getUserRoles();
        const role = await getUserRole();

        setUserRole(role);
        setUserRoles(roles);
        setRoleCheckComplete(true);

        // If user is not a host, redirect to profile
        if (!roles.includes("host") && role !== "admin") {
          navigate("/profile");
          return;
        }

        await fetchHostData();
      } catch (error: any) {
        console.error("Error in role check or data fetch:", error);
        toast.error("Failed to load dashboard data");
        setRoleCheckComplete(true);
        // Don't redirect on error, let user try again
      } finally {
        setLoading(false);
      }
    };

    // Add a small delay before checking roles to ensure auth is ready
    const timer = setTimeout(checkUserRole, 200);
    return () => clearTimeout(timer);
  }, [user, navigate, getUserRole, getUserRoles]);

  const fetchHostData = async () => {
    if (!user) return;

    try {
      // Fetch host properties
      const { data: propertiesData, error: propertiesError } = await supabase
        .from("properties")
        .select("*")
        .eq("owner_id", user.id);

      if (propertiesError) throw propertiesError;
      setProperties(propertiesData || []);

      // Fetch property IDs for bookings query
      const propertyIds = propertiesData?.map((property) => property.id) || [];

      if (propertyIds.length > 0) {
        // Fetch bookings for host properties
        const { data: bookingsData, error: bookingsError } = await supabase
          .from("bookings")
          .select("*")
          .in("property_id", propertyIds)
          .order("created_at", { ascending: false });

        if (bookingsError) throw bookingsError;
        setBookings(bookingsData || []);

        // Calculate total earnings
        const earnings = bookingsData?.reduce((total, booking) => {
          return (
            total +
            (booking.status === "confirmed" ? Number(booking.total_price) : 0)
          );
        }, 0);

        setTotalEarnings(earnings || 0);
      }

      // Also fetch car listings if available
      const { data: carsData, error: carsError } = await supabase
        .from("cars")
        .select("*")
        .eq("owner_id", user.id);

      if (!carsError && carsData) {
        // Add car data to properties for display
        setProperties([...(propertiesData || []), ...(carsData || [])]);

        // Fetch car bookings
        const carIds = carsData?.map((car) => car.id) || [];

        if (carIds.length > 0) {
          const { data: carBookingsData, error: carBookingsError } =
            await supabase
              .from("car_bookings")
              .select("*")
              .in("car_id", carIds)
              .order("created_at", { ascending: false });

          if (!carBookingsError && carBookingsData) {
            // Add car bookings to total bookings
            setBookings([...(bookings || []), ...(carBookingsData || [])]);

            // Add car earnings to total earnings
            const carEarnings = carBookingsData?.reduce((total, booking) => {
              return (
                total +
                (booking.status === "confirmed"
                  ? Number(booking.total_price)
                  : 0)
              );
            }, 0);

            setTotalEarnings(totalEarnings + (carEarnings || 0));
          }
        }
      }
    } catch (error: any) {
      console.error("Error fetching host data:", error);
      toast.error("Failed to load host data");
    }
  };

  if (loading || !roleCheckComplete) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  // If role check is complete but user is not authorized, show error
  if (
    roleCheckComplete &&
    !userRoles.includes("host") &&
    userRole !== "admin"
  ) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You need to be a host to access this page.
          </p>
          <button
            onClick={() => navigate("/profile")}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Host Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Listings
            </CardTitle>
            <Home className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{properties.length}</div>
            <p className="text-xs text-muted-foreground">
              Properties and vehicles you have listed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Bookings
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings.length}</div>
            <p className="text-xs text-muted-foreground">
              All-time bookings for your listings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Earnings
            </CardTitle>
            <DollarSign className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalEarnings.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              From confirmed bookings
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="listings" className="w-full">
        <TabsList className="grid grid-cols-4 mb-8">
          <TabsTrigger value="listings">
            <Home className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">My Listings</span>
          </TabsTrigger>
          <TabsTrigger value="bookings">
            <CalendarDays className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Reservations</span>
          </TabsTrigger>
          <TabsTrigger value="earnings">
            <DollarSign className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Earnings</span>
          </TabsTrigger>
          <TabsTrigger value="messages">
            <MessageSquare className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Messages</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="listings" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Your Listings</h2>
            <div className="space-x-2">
              <button
                onClick={() => navigate("/listings/create")}
                className="px-3 py-1 bg-black text-white rounded-md text-sm"
              >
                Add Property
              </button>
              <button
                onClick={() => navigate("/cars/create")}
                className="px-3 py-1 bg-black text-white rounded-md text-sm"
              >
                Add Car
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {properties.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    You haven't created any listings yet.
                  </p>
                  <div className="flex justify-center mt-4 space-x-2">
                    <button
                      onClick={() => navigate("/listings/create")}
                      className="px-3 py-1 bg-black text-white rounded-md text-sm"
                    >
                      Create Property Listing
                    </button>
                    <button
                      onClick={() => navigate("/cars/create")}
                      className="px-3 py-1 bg-black text-white rounded-md text-sm"
                    >
                      Create Car Listing
                    </button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              properties.map((property: any) => (
                <Card key={property.id} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <img
                      src={property.images?.[0] || "/placeholder.svg"}
                      alt={property.title || property.make}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-bold truncate">
                      {property.title || `${property.make} ${property.model}`}
                    </h3>
                    <p className="text-sm text-muted-foreground truncate">
                      {property.location}
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <Badge
                        variant={
                          property.status === "active" ? "default" : "secondary"
                        }
                      >
                        {property.status}
                      </Badge>
                      <span className="font-semibold">
                        ${property.price || property.price_day}/day
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-4">
          <h2 className="text-xl font-bold">Reservations</h2>
          <Card>
            <CardContent className="pt-6">
              {bookings.length === 0 ? (
                <p className="text-center text-muted-foreground">
                  No reservations yet.
                </p>
              ) : (
                <div className="space-y-4">
                  {bookings.map((booking: any) => (
                    <div
                      key={booking.id}
                      className="border-b pb-4 last:border-0"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">
                            Booking #{booking.id.slice(0, 8)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {booking.check_in || booking.start_date} to{" "}
                            {booking.check_out || booking.end_date}
                          </div>
                        </div>
                        <Badge
                          variant={
                            booking.status === "confirmed"
                              ? "default"
                              : booking.status === "pending"
                              ? "secondary"
                              : booking.status === "cancelled"
                              ? "destructive"
                              : "outline"
                          }
                        >
                          {booking.status}
                        </Badge>
                      </div>
                      <div className="mt-2 flex justify-between">
                        <span className="text-sm">
                          Payment: {booking.payment_status || "pending"}
                        </span>
                        <span className="font-medium">
                          ${booking.total_price}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="earnings" className="space-y-4">
          <h2 className="text-xl font-bold">Earnings</h2>
          <Card>
            <CardHeader>
              <CardTitle>Revenue Overview</CardTitle>
              <CardDescription>
                Your earnings from confirmed bookings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <BarChart className="h-16 w-16 text-gray-300" />
                <p className="ml-4 text-muted-foreground">
                  Detailed earnings analytics will appear here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <h2 className="text-xl font-bold">Messages</h2>
          <Card>
            <CardContent className="pt-6 text-center">
              <MessageSquare className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-muted-foreground">
                Messaging feature coming soon.
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                You'll be able to communicate with your guests here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HostDashboard;
