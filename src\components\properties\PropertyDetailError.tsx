
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const PropertyDetailError = () => {
  const navigate = useNavigate();
  
  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <h2 className="text-2xl font-bold text-gray-700">
        Property not found
      </h2>
      <p className="mt-4 text-gray-500">
        The property you're looking for doesn't exist or has been removed.
      </p>
      <Button 
        onClick={() => navigate("/listings")}
        className="mt-6 bg-accent hover:bg-accent/90"
      >
        Browse other properties
      </Button>
    </div>
  );
};

export default PropertyDetailError;
