const AboutSection = () => {
  return (
    <section id="about" className="py-20 bg-white relative overflow-hidden">
      {/* Clean geometric background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-72 h-72 bg-accent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-secondary rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-block px-6 py-2 bg-accent/10 rounded-full text-accent font-semibold text-sm mb-4">
              About Gescostay
            </div>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
              Africa's Trusted Home for{" "}
              <span className="bg-gradient-to-r from-accent to-secondary bg-clip-text text-transparent">
                Travel
              </span>
            </h2>
            <div className="max-w-4xl mx-auto space-y-6">
              <p className="text-xl md:text-2xl text-gray-700 leading-relaxed font-light">
                Gescostay is more than a booking platform — it's a movement to
                transform how Africa travels, hosts, and connects with the
                world.
              </p>
              <p className="text-lg md:text-xl text-gray-600 leading-relaxed">
                We're here to redefine what it means to explore Africa:
                authentic stays, trusted communities, and seamless experiences
                powered by technology that understands the continent's heart and upbeat.
                Whether you're a traveller, a host, or part of the diaspora
                coming home — Gescostay is your platform.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">🌍</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Travelers</h3>
              <p className="text-gray-600">
                Discover authentic African experiences with verified hosts and
                secure bookings.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">🏠</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Hosts</h3>
              <p className="text-gray-600">
                Share your space, earn income, and connect with travelers from
                around the world.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-brown rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-white">❤️</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">For Diaspora</h3>
              <p className="text-gray-600">
                Reconnect with your roots through trusted accommodations and
                local experiences.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
