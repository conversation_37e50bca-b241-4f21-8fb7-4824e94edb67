import Hero from "@/components/home/<USER>";
import FeaturedListings from "@/components/home/<USER>";
import ExploreSection from "@/components/home/<USER>";
import CarRentalSection from "@/components/home/<USER>";
import AboutSection from "@/components/home/<USER>";
import WhyChooseUsSection from "@/components/home/<USER>";
import HowItWorksSection from "@/components/home/<USER>";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Home, Car, UtensilsCrossed, Music } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <Hero />
      <WhyChooseUsSection />
      <AboutSection />
      <FeaturedListings />

      {/* Categories Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Explore Our Categories
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover everything Africa has to offer - from comfortable accommodations to exciting experiences
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Accommodations */}
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-center w-16 h-16 bg-accent/10 rounded-full mb-4 mx-auto">
                <Home className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                Accommodations
              </h3>
              <p className="text-gray-600 text-center mb-4">
                Properties, Hotels, Apartments, Lodges and Guest Houses
              </p>
              <div className="space-y-2">
                <Link
                  to="/listings"
                  className="block w-full text-center bg-accent hover:bg-accent/90 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Browse Properties
                </Link>
                <Link
                  to="/hotels"
                  className="block w-full text-center bg-secondary hover:bg-secondary/90 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Browse Hotels
                </Link>
              </div>
            </div>

            {/* Car Rental */}
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-center w-16 h-16 bg-secondary/10 rounded-full mb-4 mx-auto">
                <Car className="h-8 w-8 text-secondary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                Car Rental
              </h3>
              <p className="text-gray-600 text-center mb-4">
                Reliable vehicles for your journey across Africa
              </p>
              <div className="space-y-2">
                <Link
                  to="/cars"
                  className="block w-full text-center bg-secondary hover:bg-secondary/90 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Browse Cars
                </Link>
              </div>
            </div>

            {/* Restaurants */}
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4 mx-auto">
                <UtensilsCrossed className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                Restaurants
              </h3>
              <p className="text-gray-600 text-center mb-4">
                Authentic African cuisine and international dining
              </p>
              <div className="space-y-2">
                <a
                  href="https://gescogm.com/#/produits/categorie/2840"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-center bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Explore Dining
                </a>
              </div>
            </div>

            {/* Night Life */}
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4 mx-auto">
                <Music className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                Night Life
              </h3>
              <p className="text-gray-600 text-center mb-4">
                Experience vibrant nightlife and entertainment
              </p>
              <div className="space-y-2">
                <a
                  href="http://e-tickets.online"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-center bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Find Events
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <CarRentalSection />
      <HowItWorksSection />
      {/* <ExploreSection /> */}

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-secondary/10 via-accent/10 to-secondary/10">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-brown mb-6">
              Join the Gescostay Community
            </h2>
            <p className="text-gray-600 mb-8 text-lg">
              Whether you're hosting your property or listing your car, become
              part of Africa's most trusted travel platform and start earning
              with confidence.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                className="bg-accent hover:bg-accent/90 text-white px-8 py-6 text-lg"
                asChild
              >
                <Link to="/listings/create">Start Hosting</Link>
              </Button>
              <Button
                className="bg-secondary hover:bg-secondary/90 text-white px-8 py-6 text-lg"
                asChild
              >
                <Link to="/cars/create">List Your Car</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>



      <Footer />
    </div>
  );
};

export default Index;
