import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { OTPInput } from "@/components/ui/otp-input";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/layout/AuthProvider";
import { Loader2, Phone, Shield, ArrowLeft, Mail } from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import { supabase } from "@/integrations/supabase/client";

const OTPVerification = () => {
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [resending, setResending] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const { getUserRole } = useAuth();

  // Get verification details from navigation state
  const phoneNumber = location.state?.phoneNumber;
  const email = location.state?.email;
  const method = location.state?.method || "phone"; // Default to phone for backward compatibility

  // Check if this is a fresh session or returning after expiry
  const sessionTimestamp = location.state?.timestamp || Date.now();
  const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes

  useEffect(() => {
    if (!phoneNumber && !email) {
      navigate("/auth?mode=signup");
      return;
    }

    // Check if session has expired (more than 10 minutes old)
    const currentTime = Date.now();
    const timeDifference = currentTime - sessionTimestamp;

    if (timeDifference > SESSION_TIMEOUT) {
      setSessionExpired(true);
      toast({
        title: "Session Expired",
        description:
          "Your verification session has expired. Please request a new code.",
        variant: "destructive",
      });
      return;
    }

    // Calculate remaining time for countdown based on session age
    const elapsedSeconds = Math.floor(timeDifference / 1000);
    const remainingTime = Math.max(0, 60 - elapsedSeconds);

    setCountdown(remainingTime);
    setCanResend(remainingTime === 0);

    // Only start timer if there's time remaining
    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [phoneNumber, navigate, sessionTimestamp, toast]);

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== 6) {
      toast({
        title: "Invalid OTP",
        description: "Please enter a 6-digit verification code.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      if (method === "email") {
        // For email verification, use Supabase's built-in OTP verification
        const { error } = await supabase.auth.verifyOtp({
          email: email,
          token: otp,
          type: "email" as const,
        });

        if (error) {
          console.error("Email OTP verification error:", error);

          // Provide user-friendly error messages
          if (
            error.message.includes("Token has expired") ||
            error.message.includes("expired")
          ) {
            throw new Error(
              "Verification code has expired. Please request a new one."
            );
          } else if (
            error.message.includes("Invalid token") ||
            error.message.includes("invalid")
          ) {
            throw new Error(
              "Invalid verification code. Please check and try again."
            );
          } else if (error.message.includes("Email not confirmed")) {
            throw new Error(
              "Please check your email and enter the correct verification code."
            );
          } else {
            throw new Error(`Verification failed: ${error.message}`);
          }
        }
      } else {
        // For phone verification, use Supabase's built-in OTP
        const { error } = await supabase.auth.verifyOtp({
          phone: phoneNumber,
          token: otp,
          type: "sms" as const,
        });

        if (error) {
          throw error;
        }
      }

      toast({
        title: "Success!",
        description: `${
          method === "email" ? "Email" : "Phone number"
        } verified successfully.`,
      });

      // Ensure profile exists after verification (backup safety check)
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) {
        try {
          const { data: existingProfile } = await supabase
            .from("profiles")
            .select("id")
            .eq("id", user.id)
            .single();

          if (!existingProfile) {
            await supabase.rpc("handle_new_user_manual", {
              user_id: user.id,
              first_name: user.user_metadata?.first_name || "",
              last_name: user.user_metadata?.last_name || "",
              email: user.email || user.user_metadata?.email || "",
              phone_number:
                user.phone || user.user_metadata?.phone_number || phoneNumber,
            });
          }
        } catch (profileError) {
          console.error("Error ensuring profile exists:", profileError);
        }
      }

      // Check user role and redirect accordingly
      const userRole = await getUserRole();
      if (userRole === "host") {
        navigate("/host");
      } else if (userRole === "admin") {
        navigate("/admin/dashboard");
      } else {
        navigate("/");
      }
    } catch (error) {
      toast({
        title: "Verification Failed",
        description:
          error instanceof Error
            ? error.message
            : "Invalid verification code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setResending(true);
    try {
      if (method === "email") {
        // For email verification, use Supabase's resend confirmation
        const { error } = await supabase.auth.resend({
          type: "signup",
          email: email,
        });

        if (error) {
          console.error("Email resend error:", error);
          // Provide more specific error messages
          if (error.message.includes("Email rate limit exceeded")) {
            throw new Error(
              "Please wait before requesting another verification code."
            );
          } else if (error.message.includes("User already confirmed")) {
            throw new Error(
              "Your email is already verified. Please try signing in."
            );
          } else {
            throw new Error(
              "Failed to resend verification email. Please try again."
            );
          }
        }
      } else {
        // For phone, use Supabase's built-in resend
        const { error } = await supabase.auth.resend({
          type: "sms" as const,
          phone: phoneNumber,
        });

        if (error) {
          console.error("SMS resend error:", error);
          if (error.message.includes("SMS rate limit exceeded")) {
            throw new Error(
              "Please wait before requesting another verification code."
            );
          } else {
            throw new Error(
              "Failed to resend verification SMS. Please try again."
            );
          }
        }
      }

      toast({
        title: "OTP Resent",
        description: `A new verification code has been sent to your ${
          method === "email" ? "email" : "phone"
        }.`,
      });

      // Reset session and countdown
      setSessionExpired(false);
      setCountdown(60);
      setCanResend(false);

      // Update the session timestamp in navigation state
      navigate("/auth/verify-otp", {
        state: {
          phoneNumber,
          email,
          method,
          timestamp: Date.now(),
        },
        replace: true,
      });

      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to resend verification code.",
        variant: "destructive",
      });
    } finally {
      setResending(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50">
      <Navbar />
      <div className="flex min-h-screen flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <div className="mx-auto h-20 w-20 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mb-6 shadow-lg">
              <Shield className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-2">
              Verify Your{" "}
              {method === "email" ? "Email Address" : "Phone Number"}
            </h2>
            <p className="text-gray-600 mb-2">
              We've sent a 6-digit verification code to
            </p>
            <div className="flex items-center justify-center gap-2 mb-4">
              {method === "email" ? (
                <Mail className="h-4 w-4 text-accent" />
              ) : (
                <Phone className="h-4 w-4 text-accent" />
              )}
              <p className="text-lg font-semibold text-gray-900">
                {method === "email" ? email : phoneNumber}
              </p>
            </div>
            <p className="text-sm text-gray-500">
              Enter the code below to complete your registration
            </p>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-6 shadow-xl rounded-2xl border border-gray-100">
            <form className="space-y-6" onSubmit={handleVerifyOTP}>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4 text-center">
                  Enter Verification Code
                </label>
                <OTPInput
                  length={6}
                  value={otp}
                  onChange={setOtp}
                  disabled={loading}
                  className="mb-4"
                />
                <p className="text-xs text-gray-500 text-center">
                  {otp.length}/6 digits entered
                </p>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                disabled={loading || otp.length !== 6}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  `Verify ${method === "email" ? "Email" : "Phone Number"}`
                )}
              </Button>

              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-2">
                    Didn't receive the code?
                  </p>
                  {sessionExpired ? (
                    <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                      <p className="text-sm text-red-800 mb-3">
                        Your verification session has expired for security
                        reasons.
                      </p>
                      <Button
                        type="button"
                        onClick={handleResendOTP}
                        disabled={resending}
                        className="w-full bg-red-500 hover:bg-red-600 text-white"
                      >
                        {resending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Requesting New Code...
                          </>
                        ) : (
                          "Request New Verification Code"
                        )}
                      </Button>
                    </div>
                  ) : canResend ? (
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleResendOTP}
                      disabled={resending}
                      className="text-accent hover:text-accent/80 hover:bg-accent/5 font-medium"
                    >
                      {resending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Resending...
                        </>
                      ) : (
                        "Resend Code"
                      )}
                    </Button>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-600">
                        Resend available in{" "}
                        <span className="font-semibold text-accent">
                          {formatTime(countdown)}
                        </span>
                      </p>
                    </div>
                  )}
                </div>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500">
                      Need to change your number?
                    </span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => navigate("/auth?mode=signup")}
                  className="w-full text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Registration
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OTPVerification;
