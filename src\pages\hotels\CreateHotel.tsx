import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { LocationPicker } from "@/components/maps/LocationPicker";
import HotelAmenitiesSelector from "@/components/hotels/HotelAmenitiesSelector";
import RoomTypeManager from "@/components/hotels/RoomTypeManager";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  check_in_time: z.string().default("15:00"),
  check_out_time: z.string().default("11:00"),
  images: z.any().optional(),
});

type FormData = z.infer<typeof formSchema>;

const CreateHotel = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
    formatted_address: string;
  } | null>(null);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [createdHotelId, setCreatedHotelId] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<"hotel" | "rooms">("hotel");

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      check_in_time: "15:00",
      check_out_time: "11:00",
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!user) {
      toast.error("You must be logged in to create a hotel listing.");
      return;
    }

    setIsSubmitting(true);

    try {
      let imageUrls: string[] = [];

      // Upload images if provided
      if (data.images && data.images.length > 0) {
        const uploadPromises = Array.from(data.images).map(async (file: File) => {
          const fileExt = file.name.split('.').pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `hotels/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from('property-images')
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          const { data: { publicUrl } } = supabase.storage
            .from('property-images')
            .getPublicUrl(filePath);

          return publicUrl;
        });

        imageUrls = await Promise.all(uploadPromises);
      }

      // Create the hotel listing
      const { data: hotelData, error } = await supabase.from('hotels').insert({
        title: data.title,
        description: data.description,
        location: data.location,
        latitude: selectedLocation?.latitude || null,
        longitude: selectedLocation?.longitude || null,
        formatted_address: selectedLocation?.formatted_address || null,
        check_in_time: data.check_in_time,
        check_out_time: data.check_out_time,
        owner_id: user.id,
        images: imageUrls,
        amenities: selectedAmenities,
        policies: {},
      }).select().single();

      if (error) throw error;

      setCreatedHotelId(hotelData.id);
      setCurrentStep("rooms");

      toast.success("Hotel created successfully! Now add room types to complete your hotel listing.");
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Create Hotel Listing</CardTitle>
            <CardDescription>
              {currentStep === "hotel"
                ? "Provide information about your hotel to attract guests"
                : "Add room types and manage your hotel inventory"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={currentStep} onValueChange={(value) => setCurrentStep(value as "hotel" | "rooms")}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="hotel">Hotel Information</TabsTrigger>
                <TabsTrigger value="rooms" disabled={!createdHotelId}>Room Types</TabsTrigger>
              </TabsList>

              <TabsContent value="hotel" className="mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hotel Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Grand Hotel Banjul" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your hotel, its unique features, and what makes it special..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Banjul, The Gambia" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="check_in_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Check-in Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="check_out_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Check-out Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <LocationPicker
                onLocationSelect={setSelectedLocation}
                initialLocation={selectedLocation}
              />

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="images"
                  render={({ field: { value, onChange, ...field } }) => (
                    <FormItem>
                      <FormLabel>Images</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => onChange(e.target.files)}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              <HotelAmenitiesSelector
                selectedAmenities={selectedAmenities}
                onAmenitiesChange={setSelectedAmenities}
              />

                <Button
                  type="submit"
                  className="w-full bg-accent hover:bg-accent/90"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Creating hotel..." : "Create Hotel & Continue to Room Types"}
                </Button>
              </form>
            </Form>
            </TabsContent>

            <TabsContent value="rooms" className="mt-6">
              {createdHotelId ? (
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">Add Room Types</h3>
                    <p className="text-gray-600 mb-6">
                      Create different room categories for your hotel. You can add multiple room types with different pricing and amenities.
                    </p>
                  </div>

                  <RoomTypeManager hotelId={createdHotelId} />

                  <div className="flex justify-center space-x-4 pt-6 border-t">
                    <Button
                      variant="outline"
                      onClick={() => navigate("/host/listings")}
                    >
                      Finish Later
                    </Button>
                    <Button
                      onClick={() => navigate(`/hotels/${createdHotelId}`)}
                      className="bg-accent hover:bg-accent/90"
                    >
                      View Hotel Listing
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Please complete the hotel information first.</p>
                </div>
              )}
            </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default CreateHotel;
