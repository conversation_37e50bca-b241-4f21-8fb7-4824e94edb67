
import { useState, useEffect } from 'react';
import { Bell<PERSON><PERSON>, Check } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

type Notification = {
  id: string;
  type: 'new_booking' | 'new_listing' | 'system';
  message: string;
  read: boolean;
  created_at: string;
  link?: string;
  entity_id?: string;
};

const NotificationsPanel = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNotifications();

    // Subscribe to realtime updates for new notifications
    const channel = supabase
      .channel('admin-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'admin_notifications'
        },
        (payload) => {
          setNotifications(current => [payload.new as Notification, ...current]);
          toast.info('New notification received');
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // In a real app, we would have a table for notifications
      // For now, we'll use mock data
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'new_booking',
          message: 'New property booking received from John Doe',
          read: false,
          created_at: new Date().toISOString(),
          link: '/admin/bookings',
          entity_id: 'booking123'
        },
        {
          id: '2',
          type: 'new_listing',
          message: 'New property listing awaiting approval',
          read: false,
          created_at: new Date(Date.now() - 3600000).toISOString(),
          link: '/admin/listings',
          entity_id: 'property456'
        },
        {
          id: '3',
          type: 'system',
          message: 'System update completed successfully',
          read: true,
          created_at: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: '4',
          type: 'new_booking',
          message: 'New car booking received from Jane Smith',
          read: true,
          created_at: new Date(Date.now() - 172800000).toISOString(),
          link: '/admin/bookings',
          entity_id: 'car789'
        }
      ];

      setNotifications(mockNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
    toast.success('Notification marked as read');
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
    toast.success('All notifications marked as read');
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium">Notifications</CardTitle>
        <div className="flex items-center space-x-2">
          {unreadCount > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {unreadCount} unread
            </Badge>
          )}
          <Button variant="outline" size="sm" onClick={markAllAsRead}>
            Mark all read
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-1">
        {loading ? (
          <div className="flex justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-800"></div>
          </div>
        ) : notifications.length > 0 ? (
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div 
                key={notification.id}
                className={`flex items-start justify-between p-3 rounded-lg ${
                  notification.read ? 'bg-white' : 'bg-blue-50'
                } border`}
              >
                <div className="flex items-start gap-3">
                  <div className={`rounded-full p-2 ${
                    notification.type === 'new_booking' 
                      ? 'bg-green-100 text-green-600' 
                      : notification.type === 'new_listing'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-purple-100 text-purple-600'
                  }`}>
                    <BellRing className="h-4 w-4" />
                  </div>
                  <div>
                    <p className={`text-sm ${!notification.read ? 'font-medium' : ''}`}>
                      {notification.message}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(notification.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>
                {!notification.read && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0" 
                    onClick={() => markAsRead(notification.id)}
                  >
                    <Check className="h-4 w-4" />
                    <span className="sr-only">Mark as read</span>
                  </Button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">
            No notifications
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NotificationsPanel;
