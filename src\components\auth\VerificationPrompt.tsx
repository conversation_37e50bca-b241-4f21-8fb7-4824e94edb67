import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Shield, Phone, AlertCircle } from "lucide-react";

interface VerificationPromptProps {
  phoneNumber: string;
  onClose?: () => void;
}

const VerificationPrompt = ({
  phoneNumber,
  onClose,
}: VerificationPromptProps) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleResendVerification = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.resend({
        type: "sms",
        phone: phoneNumber,
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Verification Code Sent",
        description: "A new verification code has been sent to your phone.",
      });

      // Navigate to OTP verification page
      navigate("/auth/verify-otp", {
        state: { phoneNumber, timestamp: Date.now() },
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to send verification code.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoToVerification = () => {
    navigate("/auth/verify-otp", {
      state: { phoneNumber, timestamp: Date.now() },
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
        <div className="text-center mb-6">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
            <AlertCircle className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Account Not Verified
          </h2>
          <p className="text-gray-600 mb-4">
            Your account needs to be verified before you can sign in.
          </p>
          <div className="flex items-center justify-center gap-2 mb-4">
            <Phone className="h-4 w-4 text-accent" />
            <p className="text-sm font-medium text-gray-700">{phoneNumber}</p>
          </div>
        </div>

        <div className="space-y-4">
          <Button
            onClick={handleGoToVerification}
            className="w-full h-12 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Shield className="mr-2 h-5 w-5" />
            Continue to Verification
          </Button>

          <Button
            onClick={handleResendVerification}
            disabled={loading}
            variant="outline"
            className="w-full h-12 border-2 border-gray-200 hover:border-accent hover:bg-accent/5 font-medium rounded-xl"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Phone className="mr-2 h-4 w-4" />
                Resend Verification Code
              </>
            )}
          </Button>

          {onClose && (
            <Button
              onClick={onClose}
              variant="ghost"
              className="w-full text-gray-600 hover:text-gray-800 hover:bg-gray-50"
            >
              Cancel
            </Button>
          )}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Why verify?</strong> Verification helps us secure your
            account and ensures you can recover access if needed.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerificationPrompt;
