import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  Users,
  Minus,
  Plus,
  Trash2,
  TestTube,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { Tables } from "@/integrations/supabase/types";

type Hotel = Tables<"hotels">;
type RoomType = Tables<"room_types">;

interface HotelWithRoomTypes extends Hotel {
  room_types: RoomType[];
}

interface HotelBookingWidgetProps {
  hotel: HotelWithRoomTypes;
}

const bookingSchema = z
  .object({
    check_in: z.date({
      required_error: "Check-in date is required",
    }),
    check_out: z.date({
      required_error: "Check-out date is required",
    }),
    room_type_id: z.string().min(1, "Please select a room type"),
    adults: z.number().min(1, "At least 1 adult is required"),
    children: z.number().min(0, "Children count cannot be negative"),
    rooms_count: z.number().min(1, "At least 1 room is required"),
  })
  .refine((data) => data.check_out > data.check_in, {
    message: "Check-out date must be after check-in date",
    path: ["check_out"],
  });

type BookingFormData = z.infer<typeof bookingSchema>;

const HotelBookingWidget = ({ hotel }: HotelBookingWidgetProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(
    null
  );
  const [availableRooms, setAvailableRooms] = useState<number | null>(null);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);

  const form = useForm<BookingFormData>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      adults: 2,
      children: 0,
      rooms_count: 1,
    },
  });

  const watchedValues = form.watch();

  const checkAvailability = async () => {
    if (
      !watchedValues.room_type_id ||
      !watchedValues.check_in ||
      !watchedValues.check_out
    ) {
      return;
    }

    setIsCheckingAvailability(true);
    try {
      const { data, error } = await supabase.rpc("check_room_availability", {
        p_room_type_id: watchedValues.room_type_id,
        p_check_in: format(watchedValues.check_in, "yyyy-MM-dd"),
        p_check_out: format(watchedValues.check_out, "yyyy-MM-dd"),
        p_rooms_needed: watchedValues.rooms_count,
      });

      if (error) throw error;
      setAvailableRooms(data);
    } catch (error) {
      console.error("Error checking availability:", error);
      toast.error("Failed to check room availability");
    } finally {
      setIsCheckingAvailability(false);
    }
  };

  const calculateTotal = () => {
    if (
      !selectedRoomType ||
      !watchedValues.check_in ||
      !watchedValues.check_out
    ) {
      return 0;
    }

    const nights = Math.ceil(
      (watchedValues.check_out.getTime() - watchedValues.check_in.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    return selectedRoomType.base_price * nights * watchedValues.rooms_count;
  };

  const onSubmit = async (data: BookingFormData) => {
    if (hotel.is_dummy) {
      toast.error(
        "Cannot book test listing. This is a test listing and cannot be booked. Please choose a real listing."
      );
      return;
    }

    if (!user) {
      toast.error("Please log in to make a booking");
      return;
    }

    if (availableRooms === null || availableRooms < data.rooms_count) {
      toast.error("Not enough rooms available for your selected dates");
      return;
    }

    setIsSubmitting(true);

    try {
      const totalPrice = calculateTotal();

      const { error } = await supabase.from("hotel_bookings").insert({
        hotel_id: hotel.id,
        room_type_id: data.room_type_id,
        user_id: user.id,
        check_in: format(data.check_in, "yyyy-MM-dd"),
        check_out: format(data.check_out, "yyyy-MM-dd"),
        adults: data.adults,
        children: data.children,
        rooms_count: data.rooms_count,
        total_price: totalPrice,
        status: "pending",
        payment_status: "pending",
        guest_details: {
          adults: data.adults,
          children: data.children,
        },
      });

      if (error) throw error;

      toast.success("Your booking request has been submitted successfully!");

      form.reset();
      setSelectedRoomType(null);
      setAvailableRooms(null);
    } catch (error) {
      console.error("Booking error:", error);
      toast.error("Failed to submit your booking. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRoomTypeChange = (roomTypeId: string) => {
    const roomType = hotel.room_types.find((rt) => rt.id === roomTypeId);
    setSelectedRoomType(roomType || null);
    setAvailableRooms(null);
  };

  const adjustGuests = (type: "adults" | "children", increment: boolean) => {
    const currentValue = form.getValues(type);
    const newValue = increment
      ? currentValue + 1
      : Math.max(type === "adults" ? 1 : 0, currentValue - 1);
    form.setValue(type, newValue);
  };

  const adjustRooms = (increment: boolean) => {
    const currentValue = form.getValues("rooms_count");
    const newValue = increment
      ? currentValue + 1
      : Math.max(1, currentValue - 1);
    form.setValue("rooms_count", newValue);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        {hotel.is_dummy && (
          <div className="mb-4">
            <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1 w-fit">
              <TestTube className="w-3 h-3" />
              For Display Only
            </Badge>
          </div>
        )}
        <CardTitle>Book Your Stay</CardTitle>
        <CardDescription>
          {selectedRoomType
            ? `${selectedRoomType.name} - $${selectedRoomType.base_price}/night`
            : "Select dates and room type"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Room Type Selection */}
            <FormField
              control={form.control}
              name="room_type_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Room Type</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleRoomTypeChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select room type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {hotel.room_types.map((roomType) => (
                        <SelectItem key={roomType.id} value={roomType.id}>
                          <div className="flex justify-between items-center w-full">
                            <span>{roomType.name}</span>
                            <span className="ml-2 font-medium">
                              ${roomType.base_price}/night
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Selection */}
            <div className="grid grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="check_in"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Check-in</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "MMM dd")
                            ) : (
                              <span>Pick date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="check_out"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Check-out</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "MMM dd")
                            ) : (
                              <span>Pick date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date <=
                              (form.getValues("check_in") || new Date()) ||
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Guests and Rooms */}
            <div className="space-y-3">
              <Label>Guests & Rooms</Label>

              <div className="space-y-3 border rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Adults</Label>
                    <p className="text-xs text-gray-500">Ages 13+</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustGuests("adults", false)}
                      disabled={watchedValues.adults <= 1}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center">
                      {watchedValues.adults}
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustGuests("adults", true)}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Children</Label>
                    <p className="text-xs text-gray-500">Ages 0-12</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustGuests("children", false)}
                      disabled={watchedValues.children <= 0}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center">
                      {watchedValues.children}
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustGuests("children", true)}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Rooms</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustRooms(false)}
                      disabled={watchedValues.rooms_count <= 1}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center">
                      {watchedValues.rooms_count}
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => adjustRooms(true)}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Check Availability Button */}
            {watchedValues.room_type_id &&
              watchedValues.check_in &&
              watchedValues.check_out && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={checkAvailability}
                  disabled={isCheckingAvailability}
                >
                  {isCheckingAvailability
                    ? "Checking..."
                    : "Check Availability"}
                </Button>
              )}

            {/* Availability Status */}
            {availableRooms !== null && (
              <div className="p-3 rounded-lg bg-gray-50">
                {availableRooms >= watchedValues.rooms_count ? (
                  <p className="text-green-600 text-sm font-medium">
                    ✓ {availableRooms} rooms available
                  </p>
                ) : (
                  <p className="text-red-600 text-sm font-medium">
                    ✗ Only {availableRooms} rooms available (you need{" "}
                    {watchedValues.rooms_count})
                  </p>
                )}
              </div>
            )}

            {/* Price Summary */}
            {selectedRoomType &&
              watchedValues.check_in &&
              watchedValues.check_out && (
                <div className="space-y-2">
                  <Separator />
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>
                        ${selectedRoomType.base_price} ×{" "}
                        {Math.ceil(
                          (watchedValues.check_out.getTime() -
                            watchedValues.check_in.getTime()) /
                            (1000 * 60 * 60 * 24)
                        )}{" "}
                        nights × {watchedValues.rooms_count} rooms
                      </span>
                      <span>${calculateTotal()}</span>
                    </div>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>${calculateTotal()}</span>
                  </div>
                </div>
              )}

            {/* Submit Button */}
            <Button
              type="submit"
              className={`w-full bg-accent hover:bg-accent/90 ${
                hotel.is_dummy ? "cursor-not-allowed opacity-50" : ""
              }`}
              disabled={
                hotel.is_dummy ||
                isSubmitting ||
                !user ||
                availableRooms === null ||
                availableRooms < watchedValues.rooms_count
              }
            >
              {hotel.is_dummy
                ? "Test Listing - Cannot Book"
                : isSubmitting
                ? "Booking..."
                : !user
                ? "Login to Book"
                : "Reserve Now"}
            </Button>

            {!user && (
              <p className="text-xs text-gray-500 text-center">
                You'll be redirected to login before completing your booking
              </p>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default HotelBookingWidget;
